<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <!-- Modern App Bar Layout -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        app:elevation="0dp">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsingToolbar"
            android:layout_width="match_parent"
            android:layout_height="320dp"
            app:layout_scrollFlags="scroll|exitUntilCollapsed"
            app:contentScrim="@color/white"
            app:expandedTitleTextAppearance="@style/TextAppearance.App.CollapsingToolbar.Expanded"
            app:collapsedTitleTextAppearance="@style/TextAppearance.App.CollapsingToolbar.Collapsed"
            app:expandedTitleMarginStart="24dp"
            app:expandedTitleMarginBottom="80dp">

            <!-- Hero Shop Cover Image -->
            <ImageView
                android:id="@+id/shopCoverImageView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@color/background_light"
                app:layout_collapseMode="parallax"
                tools:src="@drawable/shop_cover_placeholder" />

            <!-- Modern Gradient Overlay -->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/modern_gradient_overlay" />

            <!-- Modern Toolbar -->
            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin"
                app:navigationIcon="@drawable/ic_arrow_back"
                app:navigationIconTint="@color/white">

                <!-- Notification Icon -->
                <ImageView
                    android:id="@+id/notificationIcon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="end"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/ic_notification"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="4dp"
                    app:tint="@color/white" />

            </androidx.appcompat.widget.Toolbar>

            <!-- Shop Title Overlay -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:orientation="vertical"
                android:padding="24dp"
                android:layout_marginBottom="16dp">

                <TextView
                    android:id="@+id/shopNameTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="32sp"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-medium"
                    android:shadowColor="@color/black"
                    android:shadowDx="0"
                    android:shadowDy="2"
                    android:shadowRadius="8"
                    tools:text="Test Shop" />

            </LinearLayout>

        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@color/white">

            <!-- Shop Info Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:cardCornerRadius="16dp"
                app:cardBackgroundColor="@color/white"
                app:cardElevation="8dp"
                app:strokeWidth="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Address with Location Icon -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_location"
                            app:tint="@color/colorPrimary"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/shopAddressTextView"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp"
                            android:lineSpacingExtra="2dp"
                            tools:text="R313, Sreepur, Sreepur Upazila (Gazipur), Gazipur District, Dhaka Division, Bangladesh" />

                        <!-- Status Badge -->
                        <TextView
                            android:id="@+id/statusBadge"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/modern_status_badge"
                            android:paddingHorizontal="12dp"
                            android:paddingVertical="6dp"
                            android:text="@string/closed"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            tools:text="Closed" />

                    </LinearLayout>

                    <!-- Rating Section -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="20dp">

                        <!-- Rating Stars -->
                        <LinearLayout
                            android:id="@+id/ratingStarsLayout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">
                            <!-- Stars will be added programmatically -->
                        </LinearLayout>

                        <TextView
                            android:id="@+id/ratingTextView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            tools:text="(3.0)" />

                    </LinearLayout>

                    <!-- Action Buttons -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center">

                        <!-- Call Button -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/callButton"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="0dp"
                            android:layout_height="48dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:text="@string/call"
                            android:textColor="@color/primary"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            app:icon="@drawable/ic_phone"
                            app:iconTint="@color/primary"
                            app:iconSize="18dp"
                            app:strokeColor="@color/primary"
                            app:strokeWidth="1dp"
                            app:cornerRadius="24dp" />

                        <!-- Directions Button -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/directionsButton"
                            style="@style/Widget.Material3.Button"
                            android:layout_width="0dp"
                            android:layout_height="48dp"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:text="@string/directions"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            app:icon="@drawable/ic_directions"
                            app:iconTint="@color/white"
                            app:iconSize="18dp"
                            app:backgroundTint="@color/success"
                            app:cornerRadius="24dp" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Operating Hours Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardBackgroundColor="@color/white"
                app:cardElevation="8dp"
                app:strokeWidth="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/operating_hours"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <!-- Modern Operating Hours List -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!-- Monday - Friday -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingVertical="8dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Monday - Friday"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp"
                                android:textStyle="normal" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="9:00 AM - 6:00 PM"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />

                        </LinearLayout>

                        <!-- Saturday -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingVertical="8dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Saturday"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp"
                                android:textStyle="normal" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="10:00 AM - 4:00 PM"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />

                        </LinearLayout>

                        <!-- Sunday -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingVertical="8dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Sunday"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp"
                                android:textStyle="normal" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Closed"
                                android:textColor="@color/error"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Services Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="16dp">

                <!-- Services Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_service"
                        app:tint="@color/primary"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Services"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- Services RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/detailsRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    android:clipToPadding="false" />

            </LinearLayout>

            <!-- Items Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="100dp">

                <!-- Items Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_laundry_item"
                        app:tint="@color/primary"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Items"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- Empty State for Items -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="40dp">

                    <ImageView
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:src="@drawable/ic_empty_box"
                        app:tint="@color/text_hint"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="No items listed yet."
                        android:textColor="@color/text_hint"
                        android:textSize="16sp"
                        android:gravity="center" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Modern Place Order Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/orderFab"
        style="@style/Widget.Material3.Button"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_gravity="bottom"
        android:layout_margin="16dp"
        android:text="Place Order"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        app:icon="@drawable/ic_shopping_cart"
        app:iconTint="@color/white"
        app:iconSize="20dp"
        app:backgroundTint="@color/colorPrimary"
        app:cornerRadius="28dp" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
